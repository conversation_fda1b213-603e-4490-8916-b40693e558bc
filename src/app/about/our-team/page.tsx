'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { MemberModal } from '@/components/ui/MemberModal'
import Image from 'next/image'
import {
  floating
} from '@/lib/animations'

export default function OurTeamPage() {
  const [selectedMember, setSelectedMember] = useState<typeof allMembers[0] | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openMemberModal = (member: typeof allMembers[0]) => {
    setSelectedMember(member)
    setIsModalOpen(true)
  }

  const closeMemberModal = () => {
    setIsModalOpen(false)
    setSelectedMember(null)
  }

  // Advisor (teacher)
  const advisor = {
    name: '施朱娟',
    introduction: ' - ',
    color: 'bg-green-500'
  }

  // All student members from all groups combined
  const allMembers = [
    // Equipment Group
    {
      name: '林秉用',
      introduction: '我是林秉用，2024年高一升高二 成功高中的學生，參加社團的目的在於製作肥皂以供自己使用，能夠盡微薄之力為團隊獻上一份力，也是我的榮幸。',
      color: 'bg-green-500',
      profileImage: '/members_profiles/林秉用.png'
    },
    {
      name: '林楷哲',
      introduction: '一年級在林秉用的介紹下接觸了High school soap lab，認識了很多很有能力的學長姐和學弟妹，希望我在未來能持續進步，為這個團隊貢獻、付出，讓更多人知道如何守護我們僅有的地球，皂顧全世界！',
      color: 'bg-green-600'
    },
    {
      name: '黃謙如',
      introduction: '小時候有做過肥皂，但不算有太多經驗，是因為喜歡手作所以決定加入這個社團。希望可以透過這個社團能夠發揮自己身為社會一分子的影響力，幫助更多人，也保護大自然的生態。',
      color: 'bg-green-700'
    },
    {
      name: '林俊祥',
      introduction: '我從高一加入HSSL,到現在過了一年，我參加過喜樂園活動還有大大小小的義賣活動，對於製作肥皂有一定的熟練度，對於任何新事物具有好奇心，有意願挑戰。',
      color: 'bg-green-800'
    },
    {
      name: '賴秉宸',
      introduction: '經過我高一的班導介紹HSSL後，就有興趣參加這個團隊，一部份是為了充實高中生活，也是希望透過這個志工團體認識到不同領域的人。期望透過這一次次的付出累積自己的經驗。',
      color: 'bg-green-900'
    },
    {
      name: '陳博軒',
      introduction: '我從國小時就非常喜歡自然科學也喜歡動手做小實驗，尤其是觀察化學變化在2025的6月我加入了HSSL 這個團隊，希望可以做環保貢獻社會，也希望能學習到化學方面的知識。',
      color: 'bg-green-950'
    },
    {
      name: '鄭嵐霙',
      introduction: '加入hssl是因為對化學以及實作製作肥皂的過程很感興趣，也希望能將這份興趣應用在服務社區的行動中。很期待能和大家一起學習、合作，在這段過程中成長、累積經驗！',
      color: 'bg-emerald-500'
    },
    {
      name: '張呈瑞',
      introduction: '我是來自康橋秀岡的張呈瑞，在同學的邀請下，我加入了 High School SoaP Lab。我期許自己能夠在團隊中學習製皂技術與化學原理，積極參與公益行動，與大家一起在每一塊肥皂中實踐理念，為團隊貢獻一份心力。',
      color: 'bg-emerald-600'
    },
    // Teaching Group
    {
      name: '莊又晰',
      introduction: '2021年和姊姊和朋友們一起成立HSSL,開始皂顧人&地球。至今學會了好多關於環保永續的理念，以及如何籌劃活動。希望可以把這份心帶入學校！邀約更多夥伴一起努力。',
      color: 'bg-green-500'
    },
    {
      name: '黃冠傑',
      introduction: '在學校經由同學的介紹，我認識了hssl這個志工團體，由於我很少聽說有志工團體是透過製造肥皂來幫助社會，所以聽到同學的介紹就滿感興趣的，希望未來也能繼續透過作肥皂來幫助需要幫助的人。',
      color: 'bg-green-600'
    },
    // Documentation Group
    {
      name: '饒子儀',
      introduction: '2024年寒假的時候接觸到了 High school soap lab ，體驗了做肥皂也解了這個社團的初衷。我非常享受做肥皂的過程也很開心可以做公益幫助更多人，希望之後能在這個社團學習到更多東西。',
      color: 'bg-green-500'
    },
    {
      name: '張育瑄',
      introduction: '國一時因為姊姊加入這個團隊  我也跟著接觸打皂，覺得很有趣，也因看著姐姐跟著這個團體學到很多東西，就希望自也能在高中加入，一起學習透過手工皂做公益，並豐富我的高中生活。',
      color: 'bg-green-600'
    },
    {
      name: '林芸安',
      introduction: '寒假的時候在Anna的帶領下認識了High SCHOOL SOAP LAB並實際體驗了製作肥皂的過程。也因此希望能夠在這段時間透過做工藝的方式幫助更多需要的人並且也了解更多關於肥皂的意義!',
      color: 'bg-green-700'
    },
    {
      name: '林祐安',
      introduction: '身位一位對插畫非常有興趣的人，我很高興能被邀請參與此社團的繪本製作。在製作繪本的過程中，我不僅學到了製作肥皂的步驟，更被這個社團的環保精神打動，意識到了愛護地球的重要性！',
      color: 'bg-green-800'
    },
    {
      name: '郭芃妘',
      introduction: '由家人在網路上發現這個志工團體，加入團隊後希望能透過自己小小的力量幫助到需要的人，並在其中學習、累積各種經驗。',
      color: 'bg-green-900'
    },
    // General Affairs Group
    {
      name: '謝舒安',
      introduction: '2021年疫情爆發的暑假，我跟著姐姐一起學習手工皂的製作，認識了手工皂的美好，參與了HSSL皂顧醫護的送皂活動覺得非常有意義!期待跟著大家繼續皂顧地球!',
      color: 'bg-emerald-500'
    },
    {
      name: '龔昀晴',
      introduction: '在疫情期間，我就有參加過HSSL舉辦的皂顧醫護的活動，也對藉由做手工皂來幫助有需要的人感興趣，在2025加入HSSL ，希望之後在這個團隊中能幫助到在社會上有需要的人。',
      color: 'bg-emerald-600'
    },
    {
      name: '劉峻成',
      introduction: '我是來自康橋秀岡的劉峻成。在同學的邀請下，我加入了 High school soap lab，也了解到了這個組織成立的初衷和重要性。我希望自己可以對於皂化反應更加熟悉，並透過製作肥皂來幫助有需要的人，並在團隊中貢獻一分心力。',
      color: 'bg-teal-600'
    },
    {
      name: '黃翊棠',
      introduction: ' - ',
      color: 'bg-teal-700'
    },
    // Information Group
    {
      name: '江新泉',
      introduction: '我經由家人的介紹而來到HSSL。我以前就有接觸過SDGs相關的志工課程，因此希望能夠藉由這次製作肥皂的活動幫助他人和環境。並且學習有關肥皂製作的知識和累積自己的志工經驗。',
      color: 'bg-green-500'
    },
    {
      name: '陳宇碩',
      introduction: '我透過高一的班導介紹，讓我知道HSSL這個團體。參加這個團體後，希望能對公共議題有更多的了解，更希望能透過自己的一小份心力影響他人。',
      color: 'bg-green-600'
    },
    {
      name: '李曜安',
      introduction: '我會加入 High School Soap Lab，是因為家人的推薦，加入後發現這個團體很適合我。不僅能手作肥皂，還能為環保與公益盡一份心力。我很期待能和大家一起努力，推廣環保理念，透過行動帶來正面的影響。',
      color: 'bg-green-700'
    },
    // Events PR Group
    {
      name: '陳品蓁',
      introduction: '經由家人的介紹加入HSSL的團隊，在加入團隊之前就有打皂的經驗，而我也希望能藉由在HSSL學到的知識幫助我們的環境，並透過HSSL辦的各種公益活動，幫助到需要的人。',
      color: 'bg-green-500'
    },
    {
      name: '侯柏任',
      introduction: '平常喜歡打籃球、健身、聽音樂。加入HSSL的目的是為了擴張自己的影響力，並且在薇閣持續這個團體。希望能為大家贏得青志獎第一名。',
      color: 'bg-green-600'
    },
    // Charity Sales Group
    {
      name: '蔡昕恩',
      introduction: '在參加了一場以海洋生物為主題的活動後，我對high school soap lab 有了更多的了解，皂化背後的化學反應以及組織對回收廢油的想法深深吸引我。我相信，透過製作肥皂，我們可以讓地球更美好。',
      color: 'bg-teal-500'
    },
    {
      name: '陳語欣',
      introduction: '在朋友的邀請下我加入了HSSL，我希望在接下來的活動中，能更投入其中，不只是學習新的東西，也希望能多認識一些人，並幫助一些有需要幫助的人。也希望透過這個團隊，讓更多人一起關注和支持那些面對困難的人。',
      color: 'bg-teal-600'
    },
    {
      name: '蔡昀恩',
      introduction: ' - ',
      color: 'bg-teal-700'
    },
    {
      name: '林祖妤',
      introduction: '我是在我媽的介紹下認識這個社團,看到這個社團在疫情時做的貢獻時讓我也想加入這社團為這社會做一點付出和幫助其他人。',
      color: 'bg-teal-800'
    }
  ]

  return (
    <div className="min-h-screen bg-cream">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-600 via-green-700 to-green-800"></div>
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 60, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.h1
              className="text-4xl md:text-6xl font-bold text-white mb-6"
              variants={floating}
              initial="initial"
              animate="animate"
            >
              我們的團隊
            </motion.h1>
            <motion.p
              className="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              HSSL 團隊成員，共同推動環保教育使命
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Advisor Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-cream">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">指導老師</h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                提供專業指導與學術支持，引領團隊朝向永續發展目標前進
              </p>
            </div>

            <div className="flex justify-center">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                viewport={{ once: true }}
              >
                <Card
                  hover
                  className="w-80 shadow-lg cursor-pointer transition-all duration-200 hover:shadow-xl"
                  onClick={() => openMemberModal(advisor)}
                >
                  <CardContent className="p-8 text-center">
                    {/* Avatar */}
                    <div className="w-32 h-32 rounded-full overflow-hidden mx-auto mb-6 shadow-md">
                      <Image
                        src="/hssl_profile.jpg"
                        alt={advisor.name}
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Info */}
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {advisor.name}
                    </h3>

                    {/* Click hint */}
                    <p className="text-gray-500 text-sm">
                      查看詳細介紹
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Student Members Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-cream">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">學生成員</h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                認識我們的每一位學生成員
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {allMembers.map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.02 }}
                  viewport={{ once: true }}
                >
                  <Card
                    hover
                    className="h-full shadow-md cursor-pointer transition-all duration-200 hover:shadow-lg"
                    onClick={() => openMemberModal(member)}
                  >
                    <CardContent className="p-6 text-center">
                      {/* Avatar */}
                      <div className="w-20 h-20 rounded-full overflow-hidden mx-auto mb-4 shadow-sm">
                        <Image
                          src={member.profileImage || "/hssl_profile.jpg"}
                          alt={member.name}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Info */}
                      <h3 className="text-lg font-bold text-gray-900 mb-2">
                        {member.name}
                      </h3>

                      {/* Click hint */}
                      <p className="text-gray-500 text-sm">
                        查看詳細介紹
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Member Modal */}
      {selectedMember && (
        <MemberModal
          isOpen={isModalOpen}
          onClose={closeMemberModal}
          member={selectedMember}
          borderColor="border-green-200"
        />
      )}
    </div>
  )
}